# Test Refactoring Guidelines

## Overview
This document provides guidelines for refactoring test classes to improve maintainability, readability, and reduce code duplication. These guidelines are based on the successful refactoring of the `TechnicalMarginsTests` class.

## Core Principles

### 1. Reduce Code Duplication
- Extract repetitive object creation patterns into helper methods
- Create factory methods for commonly used test objects
- Consolidate similar test execution patterns

### 2. Improve Readability
- Test methods should focus on the "what" (test intent) rather than the "how" (setup details)
- Use descriptive helper method names that clearly indicate their purpose
- Keep test methods concise and focused on assertions

### 3. Maintain Test Independence
- Each test should be able to run independently
- Avoid shared mutable state between tests
- Use helper methods that create fresh objects for each test

## Refactoring Patterns

### Factory Methods for Test Objects

**Before:**
```csharp
[Fact]
public void TestMethod()
{
    var config = new TechnicalMarginConfiguration
    {
        FromNumberOfPassengers = 1,
        Context = MarginContext.Base,
        Margin = new MarginConfiguration { Amount = 10, MarginType = MarginType.Absolute, Currency = "EUR" },
        ProviderCode = null,
        Supplier = null,
        AirlineCodes = [],
        Passenger = PassengerType.All
    };
    // ... rest of test
}
```

**After:**
```csharp
[Fact]
public void TestMethod()
{
    var config = CreateBaseMarginConfiguration(amount: 10);
    // ... rest of test
}

private TechnicalMarginConfiguration CreateBaseMarginConfiguration(
    int fromPassengers = 1,
    decimal amount = 10,
    MarginType marginType = MarginType.Absolute,
    string currency = "EUR",
    int? providerCode = null,
    string supplier = null,
    string[] airlineCodes = null,
    PassengerType passengerType = PassengerType.All)
{
    return new TechnicalMarginConfiguration
    {
        FromNumberOfPassengers = fromPassengers,
        Context = MarginContext.Base,
        Margin = new MarginConfiguration { Amount = amount, MarginType = marginType, Currency = currency },
        ProviderCode = providerCode,
        Supplier = supplier,
        AirlineCodes = airlineCodes ?? [],
        Passenger = passengerType
    };
}
```

### Test Execution Helpers

**Before:**
```csharp
[Fact]
public void TestMethod()
{
    MockMarginConfiguration(margin);
    var result = _sut.GetPricesWithMargin(_defaultContext, originalPrices);
    result.Should().BeEquivalentTo(expectedPrices);
}
```

**After:**
```csharp
[Fact]
public void TestMethod()
{
    ExecuteMarginTest([margin], originalPrices, expectedPrices);
}

private void ExecuteMarginTest(TechnicalMarginConfiguration[] margins, 
    List<PriceCacheEntry> originalPrices, List<PriceCacheEntry> expectedPrices)
{
    MockMarginConfiguration(margins);
    var result = _sut.GetPricesWithMargin(_defaultContext, originalPrices);
    result.Should().BeEquivalentTo(expectedPrices);
}
```

### Collection Creation Helpers

**Before:**
```csharp
var prices = new List<PriceCacheEntry>
{
    new PriceCacheEntry(100, 20, 1),
    new PriceCacheEntry(110, 22, 2),
    new PriceCacheEntry(120, 24, 3)
};
```

**After:**
```csharp
var prices = CreatePriceList((100, 20, 1), (110, 22, 2), (120, 24, 3));

private List<PriceCacheEntry> CreatePriceList(params (decimal basePrice, decimal taxPrice, int minPaxes)[] prices)
{
    return prices.Select(p => new PriceCacheEntry(p.basePrice, p.taxPrice, p.minPaxes)).ToList();
}
```

## File Organization

### Class Structure
1. **Constants and Fields** - At the top
2. **Constructor** - Setup and initialization
3. **Test Methods** - Main body of the class
4. **Helper Methods** - At the bottom of the class
5. **Mock Setup Methods** - At the very end

### Naming Conventions
- **Factory Methods**: `Create{ObjectType}` (e.g., `CreateBaseMarginConfiguration`)
- **Execution Helpers**: `Execute{Action}Test` (e.g., `ExecuteMarginTest`)
- **Collection Helpers**: `Create{CollectionType}List` (e.g., `CreatePriceList`)

## Best Practices

### Helper Method Design
- Use **default parameters** to make helper methods flexible
- Provide **sensible defaults** for commonly used values
- Make parameters **optional** when possible to reduce noise in test calls
- Use **descriptive parameter names** that match the domain

### Avoid These Patterns
- ❌ Don't use `#region` directives - they add unnecessary complexity
- ❌ Don't create overly generic helpers that obscure test intent
- ❌ Don't share mutable objects between tests
- ❌ Don't create helpers that are only used once

### Do Use These Patterns
- ✅ Create specific factory methods for different object types
- ✅ Use method overloading for different creation scenarios
- ✅ Group related helper methods together
- ✅ Keep helper methods private to the test class

## Refactoring Process

### Step 1: Identify Patterns
- Look for repeated object creation code
- Find similar test execution flows
- Identify common assertion patterns

### Step 2: Extract Helpers
- Start with the most frequently repeated patterns
- Create factory methods with sensible defaults
- Extract common execution flows

### Step 3: Update Tests
- Replace inline object creation with helper calls
- Simplify test methods to focus on the unique aspects
- Ensure all tests still pass

### Step 4: Organize and Clean
- Move helper methods to the bottom of the class
- Remove any unused code
- Ensure consistent naming and formatting

## Validation

After refactoring, ensure:
- ✅ All tests continue to pass
- ✅ Test intent remains clear and readable
- ✅ Code duplication is significantly reduced
- ✅ Helper methods have clear, single responsibilities
- ✅ New tests can easily reuse existing helpers

## Example Results

From the TechnicalMarginsTests refactoring:
- **Reduced repetitive code** across 13 test methods
- **Improved readability** by extracting setup complexity
- **Maintained functionality** - all tests continue to pass
- **Enhanced maintainability** - changes to object structure only require helper method updates
- **Consistent patterns** - all similar tests follow the same structure

This approach resulted in cleaner, more maintainable test code while preserving all original functionality.
