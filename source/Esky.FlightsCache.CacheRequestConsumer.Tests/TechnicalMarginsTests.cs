using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing;
using Esky.FlightsCache.Processing.TechnicalMargin;
using Esky.FlightsCache.ProviderMapping;
using FluentAssertions;
using NSubstitute;

namespace Esky.FlightsCache.CacheRequestConsumer.Tests;

public class TechnicalMarginsTests
{
    private const string EUR = "EUR";
    private const string PLN = "PLN";

    private readonly PriceMarginContext _defaultContext = new (
        providerCode: 1, supplier: null, airlines: ["*"], totalSeatsLeft: null, EUR, passengerType: PassengerType.Adult);

    private readonly ITechnicalMarginService _technicalMarginService = Substitute.For<ITechnicalMarginService>();
    private readonly ICurrencyRatioProvider _currencyRatioProvider = Substitute.For<ICurrencyRatioProvider>();
    private readonly TechnicalMarginCalculator _sut;

    public TechnicalMarginsTests()
    {
        _sut = new TechnicalMarginCalculator(_technicalMarginService, _currencyRatioProvider);

        MockMarginConfiguration();
        _currencyRatioProvider.GetRatio(EUR, EUR).Returns(1);
        _currencyRatioProvider.GetRatio(EUR, PLN).Returns(4);
        _currencyRatioProvider.GetRatio(PLN, EUR).Returns(0.25m);
    }

    #region Helper Methods

    private TechnicalMarginConfiguration CreateBaseMarginConfiguration(
        int fromPassengers = 1,
        decimal amount = 10,
        MarginType marginType = MarginType.Absolute,
        string currency = EUR,
        int? providerCode = null,
        string supplier = null,
        string[] airlineCodes = null,
        PassengerType passengerType = PassengerType.All)
    {
        return new TechnicalMarginConfiguration
        {
            FromNumberOfPassengers = fromPassengers,
            Context = MarginContext.Base,
            Margin = new MarginConfiguration { Amount = amount, MarginType = marginType, Currency = currency },
            ProviderCode = providerCode,
            Supplier = supplier,
            AirlineCodes = airlineCodes ?? [],
            Passenger = passengerType
        };
    }

    private TechnicalMarginConfiguration CreatePackageMarginConfiguration(
        int fromPassengers = 1,
        decimal amount = 10,
        MarginType marginType = MarginType.Absolute,
        string currency = EUR,
        int? providerCode = null,
        string supplier = null,
        string[] airlineCodes = null,
        PassengerType passengerType = PassengerType.All)
    {
        return new TechnicalMarginConfiguration
        {
            FromNumberOfPassengers = fromPassengers,
            Context = MarginContext.Packages,
            Margin = new MarginConfiguration { Amount = amount, MarginType = marginType, Currency = currency },
            ProviderCode = providerCode,
            Supplier = supplier,
            AirlineCodes = airlineCodes ?? [],
            Passenger = passengerType
        };
    }

    private List<PriceCacheEntry> CreatePriceList(params (decimal basePrice, decimal taxPrice, int minPaxes)[] prices)
    {
        return prices.Select(p => new PriceCacheEntry(p.basePrice, p.taxPrice, p.minPaxes)).ToList();
    }

    private void ExecuteMarginTest(TechnicalMarginConfiguration[] margins, List<PriceCacheEntry> originalPrices,
        List<PriceCacheEntry> expectedPrices)
    {
        MockMarginConfiguration(margins);
        var result = _sut.GetPricesWithMargin(_defaultContext, originalPrices);
        result.Should().BeEquivalentTo(expectedPrices);
    }

    private void ExecuteSingleMarginTest(TechnicalMarginConfiguration margin, PriceCacheEntry originalPrice,
        PriceCacheEntry expectedPrice)
    {
        ExecuteMarginTest([margin], [originalPrice], [expectedPrice]);
    }

    private void ExecuteMultipleMatchesTest(TechnicalMarginConfiguration specificMargin,
        TechnicalMarginConfiguration generalMargin)
    {
        var originalPrice = new PriceCacheEntry(100, 0);
        var expectedPrice = new PriceCacheEntry(160, 0, marginIncluded: 60); // 100 + 10 = 110, then 110 * 1.5 = 165, margin = 60

        ExecuteMarginTest([specificMargin, generalMargin], [originalPrice], [expectedPrice]);
    }

    #endregion

    [Fact]
    public void WhenNoPrices_ThenNothingReturned()
    {
        var result = _sut.GetPricesWithMargin(_defaultContext, prices: []);
        result.Should().BeEmpty();
    }

    [Fact]
    public void WhenNoMargins_ThenOriginalPriceReturned()
    {
        var prices = new List<PriceCacheEntry> { new PriceCacheEntry(100, 20) };
        var result = _sut.GetPricesWithMargin(_defaultContext, prices);
        result.Should().BeEquivalentTo(prices);
    }

    [Fact]
    public void WhenAbsoluteMarginDefined_ThenBasePriceIsIncreased()
    {
        var margin = CreateBaseMarginConfiguration(amount: 10, marginType: MarginType.Absolute);
        var originalPrice = new PriceCacheEntry(100, 20);
        var expectedPrice = new PriceCacheEntry(110, 20, marginIncluded: 10);

        ExecuteSingleMarginTest(margin, originalPrice, expectedPrice);
    }

    [Fact]
    public void WhenRelativeMarginDefined_ThenPricesAreIncreased()
    {
        var margin = CreateBaseMarginConfiguration(amount: 10, marginType: MarginType.Relative);
        var originalPrice = new PriceCacheEntry(100, 20);
        var expectedPrice = new PriceCacheEntry(110, 22, marginIncluded: 12);

        ExecuteSingleMarginTest(margin, originalPrice, expectedPrice);
    }

    [Fact]
    public void WhenMultipleMarginsWithDifferentPassengerCounts_ThenCorrectMarginsAreApplied()
    {
        var margins = new[]
        {
            CreateBaseMarginConfiguration(fromPassengers: 1, amount: 5),
            CreateBaseMarginConfiguration(fromPassengers: 3, amount: 15),
            CreateBaseMarginConfiguration(fromPassengers: 5, amount: 10, marginType: MarginType.Relative)
        };

        var originalPrices = CreatePriceList(
            (100, 20, 1), (110, 22, 2), (120, 24, 3),
            (130, 26, 4), (140, 28, 5), (150, 30, 6));

        var expectedPrices = new List<PriceCacheEntry>
        {
            new PriceCacheEntry(105, 20, 1, marginIncluded: 5),      // 100 + 5 = 105
            new PriceCacheEntry(115, 22, 2, marginIncluded: 5),      // 110 + 5 = 115
            new PriceCacheEntry(140, 24, 3, marginIncluded: 20),     // 120 + 5 + 15 = 140
            new PriceCacheEntry(150, 26, 4, marginIncluded: 20),     // 130 + 5 + 15 = 150
            new PriceCacheEntry(176, 30.8m, 5, marginIncluded: 38.8m), // (140 + 5 + 15) * 1.1 = 176, 28 * 1.1 = 30.8
            new PriceCacheEntry(187, 33, 6, marginIncluded: 40)      // (150 + 5 + 15) * 1.1 = 187, 30 * 1.1 = 33
        };

        ExecuteMarginTest(margins, originalPrices, expectedPrices);
    }

    [Fact]
    public void WhenNoOriginalPrices_ThenMarginBasedEntryIsAdded()
    {
        var margins = new[]
        {
            CreateBaseMarginConfiguration(fromPassengers: 1, amount: 5),
            CreateBaseMarginConfiguration(fromPassengers: 3, amount: 10),
            CreateBaseMarginConfiguration(fromPassengers: 5, amount: 15)
        };

        var originalPrices = CreatePriceList((110, 25, 2), (130, 30, 4));

        var expectedPrices = new List<PriceCacheEntry>
        {
            new PriceCacheEntry(115, 25, 2, marginIncluded: 5),     // 110 + 5 = 115
            new PriceCacheEntry(125, 25, 3, marginIncluded: 15),    // 110 + 5 + 10 = 125
            new PriceCacheEntry(145, 30, 4, marginIncluded: 15),    // 130 + 5 + 10 = 145
            new PriceCacheEntry(160, 30, 5, marginIncluded: 30),    // 130 + 5 + 10 + 15 = 160
        };

        ExecuteMarginTest(margins, originalPrices, expectedPrices);
    }

    [Fact]
    public void WhenPackageMarginDefined_ThenBasePriceNotIncreased()
    {
        var margin = CreatePackageMarginConfiguration(amount: 10);
        var originalPrice = new PriceCacheEntry(100, 20);
        var expectedPrice = new PriceCacheEntry(100, 20, packagesAdditionalMargin: 10);

        ExecuteSingleMarginTest(margin, originalPrice, expectedPrice);
    }

    [Fact]
    public void WhenMultiplePackageMarginsWithDifferentPassengerCounts_ThenCorrectMarginsAreAccumulated()
    {
        var margins = new[]
        {
            CreatePackageMarginConfiguration(fromPassengers: 1, amount: 5),
            CreatePackageMarginConfiguration(fromPassengers: 3, amount: 15),
            CreatePackageMarginConfiguration(fromPassengers: 5, amount: 10, marginType: MarginType.Relative)
        };

        var originalPrices = CreatePriceList(
            (100, 20, 1), (110, 22, 2), (120, 24, 3),
            (130, 26, 4), (140, 28, 5), (150, 30, 6));

        var expectedPrices = new List<PriceCacheEntry>
        {
            new PriceCacheEntry(100, 20, 1, packagesAdditionalMargin: 5),      // 5 (absolute)
            new PriceCacheEntry(110, 22, 2, packagesAdditionalMargin: 5),      // 5 (absolute)
            new PriceCacheEntry(120, 24, 3, packagesAdditionalMargin: 20),     // 5 + 15 (absolute)
            new PriceCacheEntry(130, 26, 4, packagesAdditionalMargin: 20),     // 5 + 15 (absolute)
            new PriceCacheEntry(140, 28, 5, packagesAdditionalMargin: 38.8m),  // 5 + 15 + (140*0.1 + 28*0.1) = 38.8
            new PriceCacheEntry(150, 30, 6, packagesAdditionalMargin: 40)      // 5 + 15 + (150*0.1 + 30*0.1) = 40
        };

        ExecuteMarginTest(margins, originalPrices, expectedPrices);
    }

    [Fact]
    public void WhenMixedBaseAndPackageMarginsApplied_ThenBasePricesAndPackageMarginsAreHandledSeparately()
    {
        var margins = new[]
        {
            CreateBaseMarginConfiguration(fromPassengers: 1, amount: 10),
            CreatePackageMarginConfiguration(fromPassengers: 2, amount: 8),
            CreateBaseMarginConfiguration(fromPassengers: 4, amount: 5, marginType: MarginType.Relative),
            CreatePackageMarginConfiguration(fromPassengers: 4, amount: 12)
        };

        var originalPrices = CreatePriceList(
            (100, 20, 1), (120, 25, 2), (140, 30, 3), (160, 35, 4), (180, 40, 5));

        var expectedPrices = new List<PriceCacheEntry>
        {
            // Base: +10, Package: none
            CreatePrice(110, 20, 1, marginIncluded: 10),
            // Base: +10, Package: +8
            CreatePrice(130, 25, 2, marginIncluded: 10, packagesMargin: 8),
            // Base: +10, Package: +8
            CreatePrice(150, 30, 3, marginIncluded: 10, packagesMargin: 8),
            // Base: (160+10)*1.05=178.5, Tax: 35*1.05=36.75, MarginIncluded: 10****+1.75=20.25, Package: 8+12=20
            CreatePrice(178.5m, 36.75m, 4, marginIncluded: 20.25m, packagesMargin: 20),
            // Base: (180+10)*1.05=199.5, Tax: 40*1.05=42, MarginIncluded: 10+9.5+2=21.5, Package: 8+12=20
            CreatePrice(199.5m, 42, 5, marginIncluded: 21.5m, packagesMargin: 20)
        };

        ExecuteMarginTest(margins, originalPrices, expectedPrices);
    }

    [Fact]
    public void WhenMultipleProviderCodeMatches_ThenPricesAreIncreasedInOrder()
    {
        var specificMargin = CreateBaseMarginConfiguration(providerCode: 1, amount: 10);
        var generalMargin = CreateBaseMarginConfiguration(amount: 50, marginType: MarginType.Relative);

        ExecuteMultipleMatchesTest(specificMargin, generalMargin);
    }

    [Fact]
    public void WhenMultipleSupplierMatches_ThenPricesAreIncreasedInOrder()
    {
        var specificMargin = CreateBaseMarginConfiguration(supplier: "supplier", amount: 10);
        var generalMargin = CreateBaseMarginConfiguration(supplier: "*", amount: 50, marginType: MarginType.Relative);

        ExecuteMultipleMatchesTest(specificMargin, generalMargin);
    }

    [Fact]
    public void WhenMultipleAirlinesMatches_ThenPricesAreIncreasedInOrder()
    {
        var specificMargin = CreateBaseMarginConfiguration(airlineCodes: ["FR"], amount: 10);
        var generalMargin = CreateBaseMarginConfiguration(airlineCodes: ["*"], amount: 50, marginType: MarginType.Relative);

        ExecuteMultipleMatchesTest(specificMargin, generalMargin);
    }

    [Fact]
    public void WhenMultiplePassengerTypeMatches_ThenPricesAreIncreasedInOrder()
    {
        var specificMargin = CreateBaseMarginConfiguration(passengerType: PassengerType.Adult, amount: 10);
        var generalMargin = CreateBaseMarginConfiguration(passengerType: PassengerType.All, amount: 50, marginType: MarginType.Relative);

        ExecuteMultipleMatchesTest(specificMargin, generalMargin);
    }
    
    private void MockMarginConfiguration(params TechnicalMarginConfiguration[] margins)
    {
        _technicalMarginService
            .GetTechnicalMargins(Arg.Any<int>(), Arg.Any<string>(), Arg.Any<HashSet<string>>(), Arg.Any<int?>(),
                Arg.Any<PassengerType>())
            .Returns(margins);
    }
}