using Esky.FlightsCache.CurrencyProvider;
using Esky.FlightsCache.MessageContract;
using Esky.FlightsCache.Processing;
using Esky.FlightsCache.Processing.TechnicalMargin;
using Esky.FlightsCache.ProviderMapping;
using FluentAssertions;
using NSubstitute;

namespace Esky.FlightsCache.CacheRequestConsumer.Tests;

public class TechnicalMarginsTests
{
    private const string EUR = "EUR";
    private const string PLN = "PLN";

    private readonly PriceMarginContext _defaultContext = new (
        providerCode: 1, supplier: null, airlines: ["*"], totalSeatsLeft: null, EUR, passengerType: PassengerType.Adult);

    private readonly ITechnicalMarginService _technicalMarginService = Substitute.For<ITechnicalMarginService>();
    private readonly ICurrencyRatioProvider _currencyRatioProvider = Substitute.For<ICurrencyRatioProvider>();
    private readonly TechnicalMarginCalculator _sut;

    public TechnicalMarginsTests()
    {
        _sut = new TechnicalMarginCalculator(_technicalMarginService, _currencyRatioProvider);

        MockMarginConfiguration();
        _currencyRatioProvider.GetRatio(EUR, EUR).Returns(1);
        _currencyRatioProvider.GetRatio(EUR, PLN).Returns(4);
        _currencyRatioProvider.GetRatio(PLN, EUR).Returns(0.25m);
    }

    #region Helper Methods

    private TechnicalMarginConfiguration CreateBaseMarginConfiguration(
        int fromPassengers = 1,
        decimal amount = 10,
        MarginType marginType = MarginType.Absolute,
        string currency = EUR,
        int? providerCode = null,
        string supplier = null,
        string[] airlineCodes = null,
        PassengerType passengerType = PassengerType.All)
    {
        return new TechnicalMarginConfiguration
        {
            FromNumberOfPassengers = fromPassengers,
            Context = MarginContext.Base,
            Margin = new MarginConfiguration { Amount = amount, MarginType = marginType, Currency = currency },
            ProviderCode = providerCode,
            Supplier = supplier,
            AirlineCodes = airlineCodes ?? [],
            Passenger = passengerType
        };
    }

    private TechnicalMarginConfiguration CreatePackageMarginConfiguration(
        int fromPassengers = 1,
        decimal amount = 10,
        MarginType marginType = MarginType.Absolute,
        string currency = EUR,
        int? providerCode = null,
        string supplier = null,
        string[] airlineCodes = null,
        PassengerType passengerType = PassengerType.All)
    {
        return new TechnicalMarginConfiguration
        {
            FromNumberOfPassengers = fromPassengers,
            Context = MarginContext.Packages,
            Margin = new MarginConfiguration { Amount = amount, MarginType = marginType, Currency = currency },
            ProviderCode = providerCode,
            Supplier = supplier,
            AirlineCodes = airlineCodes ?? [],
            Passenger = passengerType
        };
    }

    private PriceCacheEntry CreatePrice(decimal basePrice, decimal taxPrice, int minPaxes = 1,
        decimal? marginIncluded = null, decimal? packagesMargin = null)
    {
        return new PriceCacheEntry(basePrice, taxPrice, minPaxes, marginIncluded, packagesMargin);
    }

    private List<PriceCacheEntry> CreatePriceList(params (decimal basePrice, decimal taxPrice, int minPaxes)[] prices)
    {
        return prices.Select(p => CreatePrice(p.basePrice, p.taxPrice, p.minPaxes)).ToList();
    }

    private void ExecuteMarginTest(TechnicalMarginConfiguration[] margins, List<PriceCacheEntry> originalPrices,
        List<PriceCacheEntry> expectedPrices)
    {
        MockMarginConfiguration(margins);
        var result = _sut.GetPricesWithMargin(_defaultContext, originalPrices);
        result.Should().BeEquivalentTo(expectedPrices);
    }

    private void ExecuteSingleMarginTest(TechnicalMarginConfiguration margin, PriceCacheEntry originalPrice,
        PriceCacheEntry expectedPrice)
    {
        ExecuteMarginTest([margin], [originalPrice], [expectedPrice]);
    }

    #endregion

    [Fact]
    public void WhenNoPrices_ThenNothingReturned()
    {
        var result = _sut.GetPricesWithMargin(_defaultContext, prices: []);
        result.Should().BeEmpty();
    }

    [Fact]
    public void WhenNoMargins_ThenOriginalPriceReturned()
    {
        var prices = new List<PriceCacheEntry> { new (basePrice: 100, taxPrice: 20) };
        var result = _sut.GetPricesWithMargin(_defaultContext, prices);
        result.Should().BeEquivalentTo(prices);
    }

    [Fact]
    public void WhenAbsoluteMarginDefined_ThenBasePriceIsIncreased()
    {
        MockMarginConfiguration(new TechnicalMarginConfiguration
        {
            FromNumberOfPassengers = 1,
            Context = MarginContext.Base, 
            Margin = new MarginConfiguration { Amount = 10, MarginType = MarginType.Absolute, Currency = EUR}
        });

        var originalPrice = new PriceCacheEntry(basePrice: 100, taxPrice: 20, minimumNumberOPaxes: 1);
        var expectedPrice = new PriceCacheEntry(basePrice: 110, taxPrice: 20, minimumNumberOPaxes: 1, marginIncluded: 10);
        
        var result = _sut.GetPricesWithMargin(_defaultContext, [originalPrice]);
        result.Should().BeEquivalentTo([expectedPrice]);
    }

    [Fact]
    public void WhenRelativeMarginDefined_ThenPricesAreIncreased()
    {
        MockMarginConfiguration(new TechnicalMarginConfiguration
        {
            FromNumberOfPassengers = 1,
            Context = MarginContext.Base,
            Margin = new MarginConfiguration { Amount = 10, MarginType = MarginType.Relative }
        });

        var originalPrice = new PriceCacheEntry(basePrice: 100, taxPrice: 20, minimumNumberOPaxes: 1);
        var expectedPrice = new PriceCacheEntry(basePrice: 110, taxPrice: 22, minimumNumberOPaxes: 1, marginIncluded: 12);

        var result = _sut.GetPricesWithMargin(_defaultContext, [originalPrice]);
        result.Should().BeEquivalentTo([expectedPrice]);
    }

    [Fact]
    public void WhenMultipleMarginsWithDifferentPassengerCounts_ThenCorrectMarginsAreApplied()
    {
        MockMarginConfiguration(
            new TechnicalMarginConfiguration
            {
                FromNumberOfPassengers = 1,
                Context = MarginContext.Base,
                Margin = new MarginConfiguration { Amount = 5, MarginType = MarginType.Absolute, Currency = EUR }
            },
            new TechnicalMarginConfiguration
            {
                FromNumberOfPassengers = 3,
                Context = MarginContext.Base,
                Margin = new MarginConfiguration { Amount = 15, MarginType = MarginType.Absolute, Currency = EUR }
            },
            new TechnicalMarginConfiguration
            {
                FromNumberOfPassengers = 5,
                Context = MarginContext.Base,
                Margin = new MarginConfiguration { Amount = 10, MarginType = MarginType.Relative }
            }
        );

        var originalPrices = new List<PriceCacheEntry>
        {
            new(basePrice: 100, taxPrice: 20, minimumNumberOPaxes: 1),
            new(basePrice: 110, taxPrice: 22, minimumNumberOPaxes: 2),
            new(basePrice: 120, taxPrice: 24, minimumNumberOPaxes: 3),
            new(basePrice: 130, taxPrice: 26, minimumNumberOPaxes: 4),
            new(basePrice: 140, taxPrice: 28, minimumNumberOPaxes: 5),
            new(basePrice: 150, taxPrice: 30, minimumNumberOPaxes: 6)
        };

        var expectedPrices = new List<PriceCacheEntry>
        {
            new(basePrice: 105, taxPrice: 20, minimumNumberOPaxes: 1, marginIncluded: 5),      // 100 + 5 = 105
            new(basePrice: 115, taxPrice: 22, minimumNumberOPaxes: 2, marginIncluded: 5),      // 110 + 5 = 115
            new(basePrice: 140, taxPrice: 24, minimumNumberOPaxes: 3, marginIncluded: 20),     // 120 + 5 + 15 = 140
            new(basePrice: 150, taxPrice: 26, minimumNumberOPaxes: 4, marginIncluded: 20),     // 130 + 5 + 15 = 150
            new(basePrice: 176, taxPrice: 30.8m, minimumNumberOPaxes: 5, marginIncluded: 38.8m), // (140 + 5 + 15) * 1.1 = 176, 28 * 1.1 = 30.8
            new(basePrice: 187, taxPrice: 33, minimumNumberOPaxes: 6, marginIncluded: 40)      // (150 + 5 + 15) * 1.1 = 187, 30 * 1.1 = 33
        };

        // Act
        var result = _sut.GetPricesWithMargin(_defaultContext, originalPrices);

        // Assert
        result.Should().BeEquivalentTo(expectedPrices);
    }

    [Fact]
    public void WhenNoOriginalPrices_ThenMarginBasedEntryIsAdded()
    {
        MockMarginConfiguration(
            new TechnicalMarginConfiguration
            {
                FromNumberOfPassengers = 1, Context = MarginContext.Base,
                Margin = new MarginConfiguration { Amount = 5, Currency = EUR }
            },
            new TechnicalMarginConfiguration
            {
                FromNumberOfPassengers = 3, Context = MarginContext.Base,
                Margin = new MarginConfiguration { Amount = 10, Currency = EUR }
            },
            new TechnicalMarginConfiguration
            {
                FromNumberOfPassengers = 5, Context = MarginContext.Base,
                Margin = new MarginConfiguration { Amount = 15, Currency = EUR }
            }
        );

        var originalPrices = new List<PriceCacheEntry>
        {
            new(basePrice: 110, taxPrice: 25, minimumNumberOPaxes: 2),
            new(basePrice: 130, taxPrice: 30, minimumNumberOPaxes: 4)
        };

        var expectedPrices = new List<PriceCacheEntry>
        {
            new(basePrice: 115, taxPrice: 25, minimumNumberOPaxes: 2, marginIncluded: 5),     // 110 + 5 = 115
            new(basePrice: 125, taxPrice: 25, minimumNumberOPaxes: 3, marginIncluded: 15),    // 110 + 5 + 10 = 125
            new(basePrice: 145, taxPrice: 30, minimumNumberOPaxes: 4, marginIncluded: 15),    // 130 + 5 + 10 = 145
            new(basePrice: 160, taxPrice: 30, minimumNumberOPaxes: 5, marginIncluded: 30),    // 130 + 5 + 10 + 15 = 160
        };

        // Act
        var result = _sut.GetPricesWithMargin(_defaultContext, originalPrices);

        // Assert
        result.Should().BeEquivalentTo(expectedPrices);
    }

    [Fact]
    public void WhenPackageMarginDefined_ThenBasePriceNotIncreased()
    {
        MockMarginConfiguration(new TechnicalMarginConfiguration
        {
            FromNumberOfPassengers = 1,
            Context = MarginContext.Packages, 
            Margin = new MarginConfiguration { Amount = 10, MarginType = MarginType.Absolute, Currency = EUR}
        });

        var originalPrice = new PriceCacheEntry(basePrice: 100, taxPrice: 20, minimumNumberOPaxes: 1);
        var expectedPrice = new PriceCacheEntry(basePrice: 100, taxPrice: 20, minimumNumberOPaxes: 1, marginIncluded: null, packagesAdditionalMargin: 10);
        
        var result = _sut.GetPricesWithMargin(_defaultContext, [originalPrice]);
        result.Should().BeEquivalentTo([expectedPrice]);
    }

    [Fact]
    public void WhenMultiplePackageMarginsWithDifferentPassengerCounts_ThenCorrectMarginsAreAccumulated()
    {
        MockMarginConfiguration(
            new TechnicalMarginConfiguration
            {
                FromNumberOfPassengers = 1,
                Context = MarginContext.Packages,
                Margin = new MarginConfiguration { Amount = 5, MarginType = MarginType.Absolute, Currency = EUR }
            },
            new TechnicalMarginConfiguration
            {
                FromNumberOfPassengers = 3,
                Context = MarginContext.Packages,
                Margin = new MarginConfiguration { Amount = 15, MarginType = MarginType.Absolute, Currency = EUR }
            },
            new TechnicalMarginConfiguration
            {
                FromNumberOfPassengers = 5,
                Context = MarginContext.Packages,
                Margin = new MarginConfiguration { Amount = 10, MarginType = MarginType.Relative }
            }
        );

        var originalPrices = new List<PriceCacheEntry>
        {
            new(basePrice: 100, taxPrice: 20, minimumNumberOPaxes: 1),
            new(basePrice: 110, taxPrice: 22, minimumNumberOPaxes: 2),
            new(basePrice: 120, taxPrice: 24, minimumNumberOPaxes: 3),
            new(basePrice: 130, taxPrice: 26, minimumNumberOPaxes: 4),
            new(basePrice: 140, taxPrice: 28, minimumNumberOPaxes: 5),
            new(basePrice: 150, taxPrice: 30, minimumNumberOPaxes: 6)
        };

        var expectedPrices = new List<PriceCacheEntry>
        {
            new(basePrice: 100, taxPrice: 20, minimumNumberOPaxes: 1, marginIncluded: null, packagesAdditionalMargin: 5),      // 5 (absolute)
            new(basePrice: 110, taxPrice: 22, minimumNumberOPaxes: 2, marginIncluded: null, packagesAdditionalMargin: 5),      // 5 (absolute)
            new(basePrice: 120, taxPrice: 24, minimumNumberOPaxes: 3, marginIncluded: null, packagesAdditionalMargin: 20),     // 5 + 15 (absolute)
            new(basePrice: 130, taxPrice: 26, minimumNumberOPaxes: 4, marginIncluded: null, packagesAdditionalMargin: 20),     // 5 + 15 (absolute)
            new(basePrice: 140, taxPrice: 28, minimumNumberOPaxes: 5, marginIncluded: null, packagesAdditionalMargin: 38.8m),  // 5 + 15 + (140*0.1 + 28*0.1) = 38.8
            new(basePrice: 150, taxPrice: 30, minimumNumberOPaxes: 6, marginIncluded: null, packagesAdditionalMargin: 40)      // 5 + 15 + (150*0.1 + 30*0.1) = 40
        };

        // Act
        var result = _sut.GetPricesWithMargin(_defaultContext, originalPrices);

        // Assert
        result.Should().BeEquivalentTo(expectedPrices);
    }

    [Fact]
    public void WhenMixedBaseAndPackageMarginsApplied_ThenBasePricesAndPackageMarginsAreHandledSeparately()
    {
        MockMarginConfiguration(
            new TechnicalMarginConfiguration
            {
                FromNumberOfPassengers = 1,
                Context = MarginContext.Base,
                Margin = new MarginConfiguration { Amount = 10, MarginType = MarginType.Absolute, Currency = EUR }
            },
            new TechnicalMarginConfiguration
            {
                FromNumberOfPassengers = 2,
                Context = MarginContext.Packages,
                Margin = new MarginConfiguration { Amount = 8, MarginType = MarginType.Absolute, Currency = EUR }
            },
            new TechnicalMarginConfiguration
            {
                FromNumberOfPassengers = 4,
                Context = MarginContext.Base,
                Margin = new MarginConfiguration { Amount = 5, MarginType = MarginType.Relative }
            },
            new TechnicalMarginConfiguration
            {
                FromNumberOfPassengers = 4,
                Context = MarginContext.Packages,
                Margin = new MarginConfiguration { Amount = 12, MarginType = MarginType.Absolute, Currency = EUR }
            }
        );

        var originalPrices = new List<PriceCacheEntry>
        {
            new(basePrice: 100, taxPrice: 20, minimumNumberOPaxes: 1),
            new(basePrice: 120, taxPrice: 25, minimumNumberOPaxes: 2),
            new(basePrice: 140, taxPrice: 30, minimumNumberOPaxes: 3),
            new(basePrice: 160, taxPrice: 35, minimumNumberOPaxes: 4),
            new(basePrice: 180, taxPrice: 40, minimumNumberOPaxes: 5)
        };

        var expectedPrices = new List<PriceCacheEntry>
        {
            // Base: +10, Package: none
            new(basePrice: 110, taxPrice: 20, minimumNumberOPaxes: 1, marginIncluded: 10, packagesAdditionalMargin: null),
            // Base: +10, Package: +8
            new(basePrice: 130, taxPrice: 25, minimumNumberOPaxes: 2, marginIncluded: 10, packagesAdditionalMargin: 8),
            // Base: +10, Package: +8
            new(basePrice: 150, taxPrice: 30, minimumNumberOPaxes: 3, marginIncluded: 10, packagesAdditionalMargin: 8),
            // Base: (160+10)*1.05=178.5, Tax: 35*1.05=36.75, MarginIncluded: 10+8.5+1.75=20.25, Package: 8+12=20
            new(basePrice: 178.5m, taxPrice: 36.75m, minimumNumberOPaxes: 4, marginIncluded: 20.25m, packagesAdditionalMargin: 20),
            // Base: (180+10)*1.05=199.5, Tax: 40*1.05=42, MarginIncluded: 10+9.5+2=21.5, Package: 8+12=20
            new(basePrice: 199.5m, taxPrice: 42, minimumNumberOPaxes: 5, marginIncluded: 21.5m, packagesAdditionalMargin: 20)
        };

        // Act
        var result = _sut.GetPricesWithMargin(_defaultContext, originalPrices);

        // Assert
        result.Should().BeEquivalentTo(expectedPrices);
    }

    [Fact]
    public void WhenMultipleProviderCodeMatches_ThenPricesAreIncreasedInOrder()
    {
        MockMarginConfiguration(
            new TechnicalMarginConfiguration
            {
                ProviderCode = 1,
                FromNumberOfPassengers = 1,
                Context = MarginContext.Base,
                Margin = new MarginConfiguration { Amount = 10, Currency = EUR, MarginType = MarginType.Absolute }
            },
            new TechnicalMarginConfiguration
            {
                ProviderCode = null,
                FromNumberOfPassengers = 1,
                Context = MarginContext.Base,
                Margin = new MarginConfiguration { Amount = 50, MarginType = MarginType.Relative }
            });

        var originalPrice = new PriceCacheEntry(basePrice: 100, taxPrice: 0, minimumNumberOPaxes: 1);
        var expectedPrice = new PriceCacheEntry(basePrice: 160, taxPrice: 0, minimumNumberOPaxes: 1, marginIncluded: 60);

        var result = _sut.GetPricesWithMargin(_defaultContext, [originalPrice]);
        result.Should().BeEquivalentTo([expectedPrice]);
    }
    
    [Fact]
    public void WhenMultipleSupplierMatches_ThenPricesAreIncreasedInOrder()
    {
        MockMarginConfiguration(
            new TechnicalMarginConfiguration
            {
                Supplier = "supplier",
                FromNumberOfPassengers = 1,
                Context = MarginContext.Base,
                Margin = new MarginConfiguration { Amount = 10, Currency = EUR, MarginType = MarginType.Absolute }
            },
            new TechnicalMarginConfiguration
            {
                Supplier = "*",
                FromNumberOfPassengers = 1,
                Context = MarginContext.Base,
                Margin = new MarginConfiguration { Amount = 50, MarginType = MarginType.Relative }
            });

        var originalPrice = new PriceCacheEntry(basePrice: 100, taxPrice: 0, minimumNumberOPaxes: 1);
        var expectedPrice = new PriceCacheEntry(basePrice: 160, taxPrice: 0, minimumNumberOPaxes: 1, marginIncluded: 60);

        var result = _sut.GetPricesWithMargin(_defaultContext, [originalPrice]);
        result.Should().BeEquivalentTo([expectedPrice]);
    }
    
    [Fact]
    public void WhenMultipleAirlinesMatches_ThenPricesAreIncreasedInOrder()
    {
        MockMarginConfiguration(
            new TechnicalMarginConfiguration
            {
                AirlineCodes = ["FR"],
                FromNumberOfPassengers = 1,
                Context = MarginContext.Base,
                Margin = new MarginConfiguration { Amount = 10, Currency = EUR, MarginType = MarginType.Absolute }
            },
            new TechnicalMarginConfiguration
            {
                AirlineCodes = ["*"],
                FromNumberOfPassengers = 1,
                Context = MarginContext.Base,
                Margin = new MarginConfiguration { Amount = 50, MarginType = MarginType.Relative }
            });

        var originalPrice = new PriceCacheEntry(basePrice: 100, taxPrice: 0, minimumNumberOPaxes: 1);
        var expectedPrice = new PriceCacheEntry(basePrice: 160, taxPrice: 0, minimumNumberOPaxes: 1, marginIncluded: 60);

        var result = _sut.GetPricesWithMargin(_defaultContext, [originalPrice]);
        result.Should().BeEquivalentTo([expectedPrice]);
    }
    
    [Fact]
    public void WhenMultiplePassengerTypeMatches_ThenPricesAreIncreasedInOrder()
    {
        MockMarginConfiguration(
            new TechnicalMarginConfiguration
            {
                Passenger = PassengerType.Adult,
                FromNumberOfPassengers = 1,
                Context = MarginContext.Base,
                Margin = new MarginConfiguration { Amount = 10, Currency = EUR, MarginType = MarginType.Absolute }
            },
            new TechnicalMarginConfiguration
            {
                Passenger = PassengerType.All,
                FromNumberOfPassengers = 1,
                Context = MarginContext.Base,
                Margin = new MarginConfiguration { Amount = 50, MarginType = MarginType.Relative }
            });

        var originalPrice = new PriceCacheEntry(basePrice: 100, taxPrice: 0, minimumNumberOPaxes: 1);
        var expectedPrice = new PriceCacheEntry(basePrice: 160, taxPrice: 0, minimumNumberOPaxes: 1, marginIncluded: 60);

        var result = _sut.GetPricesWithMargin(_defaultContext, [originalPrice]);
        result.Should().BeEquivalentTo([expectedPrice]);
    }
    
    private void MockMarginConfiguration(params TechnicalMarginConfiguration[] margins)
    {
        _technicalMarginService
            .GetTechnicalMargins(Arg.Any<int>(), Arg.Any<string>(), Arg.Any<HashSet<string>>(), Arg.Any<int?>(),
                Arg.Any<PassengerType>())
            .Returns(margins);
    }
}